import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import * as DocumentPicker from 'expo-document-picker';
import { useAuth } from '../../contexts/AuthContext';
import { StackNavigationProp } from '@react-navigation/stack';
import { OnboardingStackParamList } from '../../navigation/OnboardingNavigator';

type Props = {
  navigation: StackNavigationProp<OnboardingStackParamList, 'DocumentUpload'>;
};

interface DocumentTemplate {
  id: string;
  document_type: string;
  display_name: string;
  description: string;
  requirements: string[];
  accepted_formats: string[];
  max_file_size: number;
  is_required: boolean;
  sort_order: number;
  help_text: string;
}

interface UploadedDocument {
  id?: string;
  document_type: string;
  file_name: string;
  file_size: number;
  file_url: string;
  mime_type: string;
  status: string;
  uri?: string; // Local URI for display
}

export default function DocumentUploadScreen({ navigation }: Props) {
  const { user, authService } = useAuth();
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState<string | null>(null);
  const [templates, setTemplates] = useState<DocumentTemplate[]>([]);
  const [uploadedDocs, setUploadedDocs] = useState<UploadedDocument[]>([]);
  const [uploadProgress, setUploadProgress] = useState<{ [key: string]: number }>({});

  useEffect(() => {
    loadDocumentTemplates();
    loadExistingDocuments();
  }, []);

  const loadDocumentTemplates = async () => {
    try {
      const supabase = authService.getSupabaseClient();
      if (!supabase) throw new Error('Supabase client not available');

      const { data, error } = await supabase
        .from('document_templates')
        .select('*')
        .eq('is_required', true)
        .order('sort_order');

      if (error) throw error;
      setTemplates(data || []);
    } catch (error) {
      console.error('Error loading document templates:', error);
      Alert.alert('Error', 'Failed to load document requirements');
    }
  };

  const loadExistingDocuments = async () => {
    try {
      const supabase = authService.getSupabaseClient();
      if (!supabase || !user?.driverProfile?.id) return;

      const { data, error } = await supabase
        .from('document_uploads')
        .select('*')
        .eq('driver_id', user.driverProfile.id);

      if (error) throw error;
      setUploadedDocs(data || []);
    } catch (error) {
      console.error('Error loading existing documents:', error);
    } finally {
      setLoading(false);
    }
  };

  const requestPermissions = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert(
        'Permission Required',
        'Please grant camera roll permissions to upload documents.'
      );
      return false;
    }
    return true;
  };

  const pickDocument = async (template: DocumentTemplate) => {
    const hasPermission = await requestPermissions();
    if (!hasPermission) return;

    Alert.alert(
      'Select Document',
      'Choose how you want to upload your document',
      [
        {
          text: 'Camera',
          onPress: () => openCamera(template),
        },
        {
          text: 'Photo Library',
          onPress: () => openImagePicker(template),
        },
        {
          text: 'Files',
          onPress: () => openDocumentPicker(template),
        },
        {
          text: 'Cancel',
          style: 'cancel',
        },
      ]
    );
  };

  const openCamera = async (template: DocumentTemplate) => {
    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: 'images',
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        await uploadDocument(template, result.assets[0]);
      }
    } catch (error) {
      console.error('Camera error:', error);
      Alert.alert('Error', 'Failed to open camera');
    }
  };

  const openImagePicker = async (template: DocumentTemplate) => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: 'images',
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        await uploadDocument(template, result.assets[0]);
      }
    } catch (error) {
      console.error('Image picker error:', error);
      Alert.alert('Error', 'Failed to open image picker');
    }
  };

  const openDocumentPicker = async (template: DocumentTemplate) => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: template.accepted_formats,
        copyToCacheDirectory: true,
      });

      if (!result.canceled && result.assets[0]) {
        await uploadDocument(template, result.assets[0]);
      }
    } catch (error) {
      console.error('Document picker error:', error);
      Alert.alert('Error', 'Failed to open document picker');
    }
  };

  const uploadDocument = async (template: DocumentTemplate, file: any) => {
    try {
      setUploading(template.document_type);
      setUploadProgress({ ...uploadProgress, [template.document_type]: 0 });

      const supabase = authService.getSupabaseClient();
      if (!supabase || !user?.id) {
        throw new Error('Authentication required');
      }

      // Validate file size
      if (file.size && file.size > template.max_file_size) {
        throw new Error(`File size exceeds ${(template.max_file_size / 1024 / 1024).toFixed(1)}MB limit`);
      }

      // Create file name using user ID (driver profile doesn't exist during onboarding)
      const fileExt = file.name?.split('.').pop() || 'jpg';
      const fileName = `${user.id}/${template.document_type}_${Date.now()}.${fileExt}`;

      // Start upload progress simulation
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          const current = prev[template.document_type] || 0;
          if (current >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return { ...prev, [template.document_type]: current + 10 };
        });
      }, 200);

      let fileUrl: string;

      try {
        // Convert file to blob for upload
        const response = await fetch(file.uri);
        const blob = await response.blob();

        console.log('Attempting to upload file:', fileName);
        console.log('File size:', blob.size);
        console.log('File type:', file.mimeType);

        // Upload to Supabase Storage
        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('driver-documents')
          .upload(fileName, blob, {
            contentType: file.mimeType || 'image/jpeg',
            upsert: true // Allow overwriting existing files
          });

        if (uploadError) {
          console.error('Storage upload error:', uploadError);
          throw new Error(`Upload failed: ${uploadError.message}`);
        }

        console.log('Upload successful:', uploadData);

        // For public buckets, get the public URL directly
        const { data: publicUrlData } = supabase.storage
          .from('driver-documents')
          .getPublicUrl(fileName);

        fileUrl = publicUrlData.publicUrl;
        console.log('File uploaded successfully to:', fileUrl);

      } catch (uploadError) {
        console.error('File upload failed:', uploadError);
        // Fallback to mock URL for development
        fileUrl = `https://example.com/documents/${fileName}`;
        console.log('Using fallback URL for development:', fileUrl);
      }

      clearInterval(progressInterval);

      // Skip database save during onboarding since driver record doesn't exist yet
      // Create a mock data object for local state
      const data = {
        id: `temp_${Date.now()}`,
        driver_id: user.id, // Use user ID temporarily
        document_type: template.document_type,
        file_name: file.name || fileName,
        file_size: file.size || 0,
        file_url: fileUrl,
        mime_type: file.mimeType || 'image/jpeg',
        status: 'pending',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      console.log('Document uploaded (stored locally during onboarding):', data);

      // Update local state
      const newDoc: UploadedDocument = {
        ...data,
        uri: file.uri, // Keep local URI for immediate display
      };

      setUploadedDocs(prev => {
        const filtered = prev.filter(doc => doc.document_type !== template.document_type);
        return [...filtered, newDoc];
      });

      setUploadProgress(prev => ({ ...prev, [template.document_type]: 100 }));

      Alert.alert('Success', `${template.display_name} uploaded successfully!`);
    } catch (error: any) {
      console.error('Upload error:', error);
      Alert.alert('Upload Failed', error.message || 'Failed to upload document');
    } finally {
      setUploading(null);
      setTimeout(() => {
        setUploadProgress(prev => {
          const newProgress = { ...prev };
          delete newProgress[template.document_type];
          return newProgress;
        });
      }, 2000);
    }
  };

  const getDocumentStatus = (documentType: string) => {
    const doc = uploadedDocs.find(d => d.document_type === documentType);
    return doc?.status || 'not_uploaded';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <Ionicons name="checkmark-circle" size={24} color="#4CAF50" />;
      case 'pending':
        return <Ionicons name="time-outline" size={24} color="#FF9800" />;
      case 'rejected':
        return <Ionicons name="close-circle" size={24} color="#F44336" />;
      default:
        return <Ionicons name="cloud-upload-outline" size={24} color="#9E9E9E" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'approved':
        return 'Approved';
      case 'pending':
        return 'Under Review';
      case 'rejected':
        return 'Rejected';
      default:
        return 'Upload Required';
    }
  };

  const canProceed = () => {
    const requiredDocs = templates.filter(t => t.is_required);
    const uploadedRequiredDocs = requiredDocs.filter(template =>
      uploadedDocs.some(doc => doc.document_type === template.document_type)
    );
    return uploadedRequiredDocs.length === requiredDocs.length;
  };

  const handleContinue = () => {
    if (!canProceed()) {
      Alert.alert('Documents Required', 'Please upload all required documents before continuing.');
      return;
    }
    // Pass uploaded documents to review screen during onboarding
    navigation.navigate('DocumentReview', { uploadedDocuments: uploadedDocs });
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#E91E63" />
          <Text style={styles.loadingText}>Loading document requirements...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.title}>Upload Documents</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <Text style={styles.subtitle}>
          Please upload the following required documents for verification:
        </Text>

        {templates.map((template) => {
          const status = getDocumentStatus(template.document_type);
          const isUploading = uploading === template.document_type;
          const progress = uploadProgress[template.document_type];
          const uploadedDoc = uploadedDocs.find(d => d.document_type === template.document_type);

          return (
            <View key={template.id} style={styles.documentCard}>
              <View style={styles.documentHeader}>
                <View style={styles.documentInfo}>
                  <Text style={styles.documentTitle}>{template.display_name}</Text>
                  <Text style={styles.documentDescription}>{template.description}</Text>
                </View>
                {getStatusIcon(status)}
              </View>

              {uploadedDoc?.uri && (
                <Image source={{ uri: uploadedDoc.uri }} style={styles.documentPreview} />
              )}

              <View style={styles.requirementsList}>
                {template.requirements.map((req, index) => (
                  <Text key={index} style={styles.requirement}>• {req}</Text>
                ))}
              </View>

              {isUploading && (
                <View style={styles.progressContainer}>
                  <View style={styles.progressBar}>
                    <View
                      style={[styles.progressFill, { width: `${progress || 0}%` }]}
                    />
                  </View>
                  <Text style={styles.progressText}>Uploading... {progress || 0}%</Text>
                </View>
              )}

              <TouchableOpacity
                style={[
                  styles.uploadButton,
                  status === 'approved' && styles.uploadButtonApproved,
                  isUploading && styles.uploadButtonDisabled,
                ]}
                onPress={() => pickDocument(template)}
                disabled={isUploading}
              >
                {isUploading ? (
                  <ActivityIndicator size="small" color="#FFF" />
                ) : (
                  <>
                    <Ionicons
                      name={status === 'not_uploaded' ? "cloud-upload" : "refresh"}
                      size={20}
                      color="#FFF"
                    />
                    <Text style={styles.uploadButtonText}>
                      {status === 'not_uploaded' ? 'Upload Document' : 'Replace Document'}
                    </Text>
                  </>
                )}
              </TouchableOpacity>

              <Text style={styles.statusText}>
                Status: {getStatusText(status)}
              </Text>

              <Text style={styles.helpText}>{template.help_text}</Text>
            </View>
          );
        })}
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity
          style={[
            styles.continueButton,
            !canProceed() && styles.continueButtonDisabled,
          ]}
          onPress={handleContinue}
          disabled={!canProceed()}
        >
          <Text style={styles.continueButtonText}>Continue to Review</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFF0FF',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
    backgroundColor: '#FFF',
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  backButton: {
    marginRight: 16,
    padding: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginVertical: 20,
    textAlign: 'center',
    lineHeight: 24,
  },
  documentCard: {
    backgroundColor: '#FFF',
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  documentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  documentInfo: {
    flex: 1,
    marginRight: 12,
  },
  documentTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  documentDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  documentPreview: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    marginBottom: 12,
    resizeMode: 'cover',
  },
  requirementsList: {
    marginBottom: 16,
  },
  requirement: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
    lineHeight: 20,
  },
  progressContainer: {
    marginBottom: 16,
  },
  progressBar: {
    height: 4,
    backgroundColor: '#F0F0F0',
    borderRadius: 2,
    overflow: 'hidden',
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#E91E63',
    borderRadius: 2,
  },
  progressText: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  uploadButton: {
    backgroundColor: '#E91E63',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    marginBottom: 12,
  },
  uploadButtonApproved: {
    backgroundColor: '#4CAF50',
  },
  uploadButtonDisabled: {
    backgroundColor: '#CCC',
  },
  uploadButtonText: {
    color: '#FFF',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'center',
  },
  helpText: {
    fontSize: 12,
    color: '#999',
    lineHeight: 18,
    textAlign: 'center',
  },
  footer: {
    padding: 24,
    backgroundColor: '#FFF',
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  continueButton: {
    backgroundColor: '#E91E63',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  continueButtonDisabled: {
    backgroundColor: '#CCC',
  },
  continueButtonText: {
    color: '#FFF',
    fontSize: 18,
    fontWeight: 'bold',
  },
});
