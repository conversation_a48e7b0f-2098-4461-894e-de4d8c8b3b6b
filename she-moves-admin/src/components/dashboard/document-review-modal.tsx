'use client'

import React, { useState } from 'react'
import { X, Check, XCircle, FileText, User, Calendar } from 'lucide-react'
import { createClientSupabase } from '@/lib/supabase'

interface DocumentReviewModalProps {
  isOpen: boolean
  onClose: () => void
  reviewId: string
  driverName: string
  documentType: string
  submittedAt: string
  onReviewComplete: () => void
}

interface DocumentData {
  id: string
  file_url: string
  file_name: string
  document_type: string
  driver_id: string
  created_at: string
  file_size?: number
  mime_type?: string
}

export function DocumentReviewModal({
  isOpen,
  onClose,
  reviewId,
  driverName,
  documentType,
  submittedAt,
  onReviewComplete
}: DocumentReviewModalProps) {
  const [loading, setLoading] = useState(false)
  const [documentData, setDocumentData] = useState<DocumentData | null>(null)
  const [reviewNotes, setReviewNotes] = useState('')
  const [rejectionReason, setRejectionReason] = useState('')
  const [qualityScore, setQualityScore] = useState<number>(3)

  const fetchDocumentData = async () => {
    if (!reviewId) return

    try {
      setLoading(true)
      const supabase = createClientSupabase()

      // Get the document data directly from document_uploads
      const { data: documentData, error: documentError } = await supabase
        .from('document_uploads')
        .select('*')
        .eq('id', reviewId)
        .single()

      if (documentError) {
        console.error('Error fetching document data:', documentError)
        return
      }

      if (documentData) {
        setDocumentData(documentData as DocumentData)
      }
    } catch (error) {
      console.error('Error fetching document data:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleApprove = async () => {
    await handleReviewAction('approved')
  }

  const handleReject = async () => {
    if (!rejectionReason.trim()) {
      alert('Please provide a rejection reason')
      return
    }
    await handleReviewAction('rejected')
  }

  const handleReviewAction = async (status: 'approved' | 'rejected') => {
    try {
      setLoading(true)
      const supabase = createClientSupabase()

      console.log('=== STARTING DOCUMENT APPROVAL PROCESS ===')
      console.log('Document ID:', reviewId)
      console.log('Status:', status)
      console.log('Document Data:', documentData)

      // Update the document_uploads record directly
      console.log('Updating document status...')
      const updatePayload: any = {
        status: status,
        reviewed_at: new Date().toISOString()
      }

      // Only add optional fields if they have values
      if (status === 'rejected' && rejectionReason) {
        updatePayload.rejection_reason = rejectionReason
      }

      if (reviewNotes) {
        updatePayload.admin_notes = reviewNotes
      }
      console.log('Update payload:', JSON.stringify(updatePayload, null, 2))

      const { data: updateData, error: updateError } = await supabase
        .from('document_uploads')
        .update(updatePayload)
        .eq('id', reviewId)
        .select()

      console.log('Document update result - data:', JSON.stringify(updateData, null, 2))
      console.log('Document update result - error:', JSON.stringify(updateError, null, 2))
      console.log('Document update result - rows affected:', updateData?.length || 0)

      if (updateError) {
        console.error('Error updating document:', updateError)
        console.error('Update error details:', JSON.stringify(updateError, null, 2))
        alert(`Failed to update document: ${updateError.message || 'Unknown error'}`)
        return
      }

      console.log('Document status updated successfully')

      // If approved, check if all driver documents are approved and update driver status
      if (status === 'approved' && documentData) {
        console.log('Checking if all driver documents are approved...')

        // Get all documents for this driver
        const { data: allDocuments, error: docsError } = await supabase
          .from('document_uploads')
          .select('status')
          .eq('driver_id', documentData.driver_id)

        if (!docsError && allDocuments) {
          const allApproved = allDocuments.every(doc => doc.status === 'approved')
          console.log('All documents approved:', allApproved)

          if (allApproved) {
            // Update driver verification status to approved
            const { error: driverError } = await supabase
              .from('drivers')
              .update({ verification_status: 'approved' })
              .eq('id', documentData.driver_id)

            if (driverError) {
              console.error('Error updating driver status:', driverError)
            } else {
              console.log('Driver verification status updated to approved')
            }
          }
        }
      }

      // Activity logging temporarily disabled due to permissions
      console.log('Activity logging skipped - approval process complete')

      onReviewComplete()
      onClose()
    } catch (error) {
      console.error('Error processing review:', error)
      alert('Failed to process review')
    } finally {
      setLoading(false)
    }
  }

  // Fetch document data when modal opens
  React.useEffect(() => {
    if (isOpen && reviewId) {
      fetchDocumentData()
    }
  }, [isOpen, reviewId])

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <FileText className="h-6 w-6 text-pink-600" />
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Document Review</h2>
              <p className="text-sm text-gray-600">{documentType}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="h-5 w-5 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="flex flex-col lg:flex-row h-[calc(90vh-200px)]">
          {/* Document Viewer */}
          <div className="flex-1 p-6 border-r border-gray-200">
            <div className="mb-4">
              <div className="flex items-center space-x-2 text-sm text-gray-600 mb-2">
                <User className="h-4 w-4" />
                <span>{driverName}</span>
              </div>
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <Calendar className="h-4 w-4" />
                <span>Submitted {new Date(submittedAt).toLocaleDateString()}</span>
              </div>
            </div>

            {loading ? (
              <div className="flex items-center justify-center h-64">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-pink-600"></div>
              </div>
            ) : documentData ? (
              <div className="border border-gray-200 rounded-lg overflow-hidden h-full">
                {/* Check if this is a placeholder URL */}
                {documentData.file_url.includes('example.com') ? (
                  <div className="flex flex-col items-center justify-center h-full text-gray-500 p-8">
                    <FileText className="h-16 w-16 mb-4 text-gray-400" />
                    <h3 className="text-lg font-medium mb-2">Document Preview Not Available</h3>
                    <p className="text-sm text-center mb-4">
                      This is a placeholder document. In a real implementation, documents would be stored in Supabase Storage.
                    </p>
                    <div className="bg-gray-100 p-4 rounded-lg w-full">
                      <h4 className="font-medium mb-2">Document Details:</h4>
                      <p><strong>File Name:</strong> {documentData.file_name}</p>
                      <p><strong>Type:</strong> {documentData.document_type}</p>
                      <p><strong>Size:</strong> {documentData.file_size ? (documentData.file_size / 1024 / 1024).toFixed(2) + ' MB' : 'Unknown'}</p>
                      <p><strong>MIME Type:</strong> {documentData.mime_type || 'Unknown'}</p>
                      <p><strong>Uploaded:</strong> {new Date(documentData.created_at).toLocaleDateString()}</p>
                    </div>
                    <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                      <p className="text-sm text-blue-700">
                        <strong>Note:</strong> To enable real document viewing, upload files to Supabase Storage and update the file_url field.
                      </p>
                    </div>
                  </div>
                ) : (
                  // Real document URLs
                  <div className="h-full">
                    {documentData.file_url.toLowerCase().includes('.pdf') ? (
                      <iframe
                        src={documentData.file_url}
                        className="w-full h-full"
                        title="Document Preview"
                        onError={() => console.error('Failed to load PDF')}
                      />
                    ) : (
                      <img
                        src={documentData.file_url}
                        alt="Document"
                        className="w-full h-full object-contain"
                        onError={() => console.error('Failed to load image')}
                      />
                    )}
                  </div>
                )}
              </div>
            ) : (
              <div className="flex items-center justify-center h-64 text-gray-500">
                <p>No document data available</p>
              </div>
            )}
          </div>

          {/* Review Panel */}
          <div className="w-full lg:w-80 p-6 bg-gray-50">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Review Actions</h3>

            {/* Quality Score */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Quality Score (1-5)
              </label>
              <select
                value={qualityScore}
                onChange={(e) => setQualityScore(Number(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pink-500"
              >
                <option value={1}>1 - Poor</option>
                <option value={2}>2 - Below Average</option>
                <option value={3}>3 - Average</option>
                <option value={4}>4 - Good</option>
                <option value={5}>5 - Excellent</option>
              </select>
            </div>

            {/* Review Notes */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Review Notes
              </label>
              <textarea
                value={reviewNotes}
                onChange={(e) => setReviewNotes(e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pink-500"
                placeholder="Add any notes about this document..."
              />
            </div>

            {/* Rejection Reason */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Rejection Reason (if rejecting)
              </label>
              <textarea
                value={rejectionReason}
                onChange={(e) => setRejectionReason(e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pink-500"
                placeholder="Explain why this document is being rejected..."
              />
            </div>

            {/* Action Buttons */}
            <div className="space-y-3">
              <button
                onClick={handleApprove}
                disabled={loading}
                className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <Check className="h-4 w-4" />
                <span>{loading ? 'Processing...' : 'Approve Document'}</span>
              </button>

              <button
                onClick={handleReject}
                disabled={loading}
                className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <XCircle className="h-4 w-4" />
                <span>{loading ? 'Processing...' : 'Reject Document'}</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
