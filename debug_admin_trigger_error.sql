-- =====================================================
-- DEBUG ADMIN DASHBOARD TRIGGER ERROR
-- Investigate the "control reached end of trigger procedure without RETURN" error
-- =====================================================

-- Step 1: Check all triggers on document_uploads table
SELECT 
    '=== TRIGGERS ON DOCUMENT_UPLOADS ===' as section,
    trigger_name,
    event_manipulation,
    action_timing,
    action_statement,
    action_condition
FROM information_schema.triggers 
WHERE event_object_table = 'document_uploads'
ORDER BY trigger_name;

-- Step 2: Check all trigger functions that might be called
SELECT 
    '=== TRIGGER FUNCTIONS ===' as section,
    proname as function_name,
    prosrc as function_body,
    CASE 
        WHEN prosrc LIKE '%RETURN NEW%' OR prosrc LIKE '%RETURN OLD%' THEN '✅ Has RETURN'
        WHEN prosrc LIKE '%RETURN%' THEN '⚠️ Has RETURN (check type)'
        ELSE '❌ Missing RETURN'
    END as return_status
FROM pg_proc 
WHERE proname IN (
    'update_updated_at_column',
    'safe_update_document_status',
    'update_verification_metrics'
)
OR prosrc LIKE '%document_uploads%'
ORDER BY proname;

-- Step 3: Check for any problematic triggers that might not return values
SELECT 
    '=== POTENTIAL PROBLEM TRIGGERS ===' as section,
    t.trigger_name,
    t.event_object_table,
    t.action_statement,
    p.proname as function_name,
    CASE 
        WHEN p.prosrc LIKE '%RETURN%' THEN 'Has RETURN'
        ELSE 'NO RETURN - PROBLEM!'
    END as return_check
FROM information_schema.triggers t
LEFT JOIN pg_proc p ON p.proname = SPLIT_PART(TRIM(BOTH '()' FROM SPLIT_PART(t.action_statement, 'EXECUTE FUNCTION', 2)), '(', 1)
WHERE t.event_object_table = 'document_uploads'
ORDER BY t.trigger_name;

-- Step 4: Check document_uploads table structure
SELECT 
    '=== DOCUMENT_UPLOADS COLUMNS ===' as section,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'document_uploads'
ORDER BY ordinal_position;

-- Step 5: Test a safe update to see what triggers fire
SELECT 
    '=== TESTING SAFE UPDATE ===' as section,
    'About to test document update...' as message;

-- Find a test document to update (if any exist)
DO $$
DECLARE
    test_doc_id UUID;
    original_updated_at TIMESTAMP;
    new_updated_at TIMESTAMP;
BEGIN
    -- Find a document to test with
    SELECT id, updated_at INTO test_doc_id, original_updated_at
    FROM document_uploads 
    LIMIT 1;
    
    IF test_doc_id IS NOT NULL THEN
        RAISE NOTICE 'Testing update on document: %', test_doc_id;
        
        -- Try a simple update that should trigger our function
        UPDATE document_uploads 
        SET admin_notes = COALESCE(admin_notes, '') || ' [Test update]'
        WHERE id = test_doc_id;
        
        -- Check if updated_at changed
        SELECT updated_at INTO new_updated_at
        FROM document_uploads 
        WHERE id = test_doc_id;
        
        IF new_updated_at > original_updated_at THEN
            RAISE NOTICE '✅ Update successful - trigger worked';
        ELSE
            RAISE NOTICE '⚠️ Update completed but updated_at did not change';
        END IF;
        
        -- Clean up test
        UPDATE document_uploads 
        SET admin_notes = REPLACE(admin_notes, ' [Test update]', '')
        WHERE id = test_doc_id;
        
    ELSE
        RAISE NOTICE 'ℹ️ No documents found to test with';
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '❌ Test update failed: %', SQLERRM;
END $$;

-- Step 6: Check for any additional triggers that might be interfering
SELECT 
    '=== ALL TRIGGERS IN DATABASE ===' as section,
    schemaname,
    tablename,
    triggername,
    CASE 
        WHEN triggername LIKE '%document%' THEN '📄 Document related'
        WHEN triggername LIKE '%update%' THEN '🔄 Update related'
        ELSE '📋 Other'
    END as category
FROM pg_trigger t
JOIN pg_class c ON t.tgrelid = c.oid
JOIN pg_namespace n ON c.relnamespace = n.oid
WHERE NOT t.tgisinternal
AND (
    c.relname = 'document_uploads' OR
    t.tgname LIKE '%document%' OR
    t.tgname LIKE '%update%'
)
ORDER BY schemaname, tablename, triggername;

SELECT 'DEBUG COMPLETE - Review results above' as final_message;
